{"value": [{"content": "Security level is set to CONFIDENTIAL. The agent maintains military-grade security standards and validates all file paths and commands for security.", "path": "system://security_info", "score": 0.8384979979134456, "metadata": {"type": "system", "category": "security", "path": "system://security_info", "doc_id": "be0e059e2d6cc50d850df3b012d5ed6a", "added_at": "2025-05-25T13:17:14.569816", "security_level": "CONFIDENTIAL", "content_hash": "e188988dda69d6c97776ee416baefd19a99a7b58718bf5abd48a70646192236a", "content_length": 148}, "relevance_explanation": "Semantic similarity: 0.838"}, {"content": "The agent can help with programming tasks, file operations, web searches, code analysis, system commands, and memory management. It formats code responses using <augment_code_snippet> tags.", "path": "system://capabilities", "score": 0.8168564374766599, "metadata": {"type": "system", "category": "capabilities", "path": "system://capabilities", "doc_id": "aeaba8c32c157751e7d72d075bf259cb", "added_at": "2025-05-25T13:17:14.575817", "security_level": "CONFIDENTIAL", "content_hash": "737cac34ccbf12fc11d2240b200c65bd36c9c96071f9a7f2bcd87332060df38f", "content_length": 189}, "relevance_explanation": "Semantic similarity: 0.817"}, {"content": "The agent uses a local LLM model at C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf with maximum context window of 131072 tokens.", "path": "system://llm_config", "score": 0.7743777851698173, "metadata": {"type": "system", "category": "configuration", "path": "system://llm_config", "doc_id": "bf7d5353b3ca7c382c302ece0c417b6f", "added_at": "2025-05-25T13:17:14.563816", "security_level": "CONFIDENTIAL", "content_hash": "6b41eb6c6196bfa2b7c045aa91c9b8e947e1b8513337dab07ce834d421ea0a8e", "content_length": 212}, "relevance_explanation": "Semantic similarity: 0.774"}, {"content": "Available tools include: write_file, read_file, remember, get_memories, search_memories, search_context, add_to_context, analyze_code, format_code, execute_command, web_search, fetch_webpage, analyze_python_code, extract_functions, extract_classes, and many more.", "path": "system://available_tools", "score": 0.6973707764433259, "metadata": {"type": "system", "category": "tools", "path": "system://available_tools", "doc_id": "db9b4ddf467e5e98d4e5f49856c60f76", "added_at": "2025-05-25T13:17:14.556817", "security_level": "CONFIDENTIAL", "content_hash": "04960d1c62a709fc687a04fa10d87876cf9f4aef5c14ff1d42c7fd556525ceb3", "content_length": 263}, "relevance_explanation": "Semantic similarity: 0.697"}, {"content": "Enhanced Military Agent is an advanced AI assistant with military-grade capabilities that behaves exactly like Augment Agent Auto. It has access to comprehensive tool integrations including file operations, web search, code analysis, process execution, and memory management.", "path": "system://agent_description", "score": 0.6762396715049119, "metadata": {"type": "system", "category": "agent_info", "path": "system://agent_description", "doc_id": "16858983d9bd773b5cd9519f4a92735a", "added_at": "2025-05-25T13:17:14.549821", "security_level": "CONFIDENTIAL", "content_hash": "c662c6d80ae2e0d63641f0e9e0759a2dfb9d11b1643298c57030f814f666aeb4", "content_length": 275}, "relevance_explanation": "Semantic similarity: 0.676"}], "expires": 1748225638.4055424, "created": 1748222038.4055424}