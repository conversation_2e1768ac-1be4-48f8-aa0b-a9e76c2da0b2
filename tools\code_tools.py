"""
Enhanced Code Tools for Military Agent - Matching Augment Agent Auto

This module provides enhanced code manipulation tools that match
Augment Agent Auto's code handling capabilities.
"""

import os
import re
import ast
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from .enhanced_tool_interface import BaseToolProvider, ToolRegistry

logger = logging.getLogger("CODE_TOOLS")


class CodeTools(BaseToolProvider):
    """
    Enhanced code tools matching Augment Agent Auto's code capabilities.
    """

    def __init__(self, registry: ToolRegistry, context_engine=None):
        """Initialize code tools."""
        self.context_engine = context_engine
        super().__init__(registry, "code")

    def setup_tools(self) -> None:
        """Setup enhanced code tools."""

        # Code analysis tools
        self.register_tool(
            "analyze_python_code",
            "Analyze Python code for structure, complexity, and issues",
            self._analyze_python_code,
            parameters={
                'code': {'type': str, 'required': False},
                'file_path': {'type': str, 'required': False},
                'include_metrics': {'type': bool, 'default': True}
            }
        )

        self.register_tool(
            "extract_functions",
            "Extract function definitions from code",
            self._extract_functions,
            parameters={
                'code': {'type': str, 'required': True},
                'language': {'type': str, 'default': 'python'}
            }
        )

        self.register_tool(
            "extract_classes",
            "Extract class definitions from code",
            self._extract_classes,
            parameters={
                'code': {'type': str, 'required': True},
                'language': {'type': str, 'default': 'python'}
            }
        )

        # Code formatting tools
        self.register_tool(
            "format_python_code",
            "Format Python code with proper indentation and style",
            self._format_python_code,
            parameters={
                'code': {'type': str, 'required': True},
                'line_length': {'type': int, 'default': 88}
            }
        )

        self.register_tool(
            "add_docstrings",
            "Add docstrings to Python functions and classes",
            self._add_docstrings,
            parameters={
                'code': {'type': str, 'required': True},
                'style': {'type': str, 'default': 'google'}
            }
        )

        # Code generation tools
        self.register_tool(
            "generate_test_template",
            "Generate test template for given code",
            self._generate_test_template,
            parameters={
                'code': {'type': str, 'required': True},
                'test_framework': {'type': str, 'default': 'unittest'}
            }
        )

        self.register_tool(
            "generate_class_template",
            "Generate class template with common methods",
            self._generate_class_template,
            parameters={
                'class_name': {'type': str, 'required': True},
                'base_class': {'type': str, 'default': None},
                'methods': {'type': list, 'default': []}
            }
        )

        # Code search and navigation
        self.register_tool(
            "find_function_calls",
            "Find all calls to a specific function in code",
            self._find_function_calls,
            parameters={
                'code': {'type': str, 'required': True},
                'function_name': {'type': str, 'required': True}
            }
        )

        self.register_tool(
            "find_imports",
            "Find all import statements in code",
            self._find_imports,
            parameters={
                'code': {'type': str, 'required': True},
                'import_type': {'type': str, 'default': 'all'}
            }
        )

    def _analyze_python_code(self, code: str = None, file_path: str = None, include_metrics: bool = True) -> str:
        """Analyze Python code structure and complexity."""
        try:
            # Get code from file_path if provided, otherwise use code parameter
            if file_path:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        code = f.read()
                except Exception as e:
                    return f"Error reading file {file_path}: {str(e)}"

            if not code:
                return "Error: No code provided. Use either 'code' or 'file_path' parameter."

            # Parse the code
            tree = ast.parse(code)

            analysis = {
                'functions': [],
                'classes': [],
                'imports': [],
                'complexity': 0,
                'lines_of_code': len(code.split('\n')),
                'issues': []
            }

            # Analyze AST
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = {
                        'name': node.name,
                        'line': node.lineno,
                        'args': [arg.arg for arg in node.args.args],
                        'docstring': ast.get_docstring(node)
                    }
                    analysis['functions'].append(func_info)

                elif isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'line': node.lineno,
                        'bases': [base.id if hasattr(base, 'id') else str(base) for base in node.bases],
                        'docstring': ast.get_docstring(node)
                    }
                    analysis['classes'].append(class_info)

                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            analysis['imports'].append(alias.name)
                    else:
                        module = node.module or ''
                        for alias in node.names:
                            analysis['imports'].append(f"{module}.{alias.name}")

            # Calculate complexity (simplified)
            analysis['complexity'] = len(analysis['functions']) + len(analysis['classes'])

            # Check for common issues
            if not analysis['functions'] and not analysis['classes']:
                analysis['issues'].append("No functions or classes found")

            for func in analysis['functions']:
                if not func['docstring']:
                    analysis['issues'].append(f"Function '{func['name']}' missing docstring")

            # Format result
            result = f"Python Code Analysis:\n"
            result += f"- Lines of code: {analysis['lines_of_code']}\n"
            result += f"- Functions: {len(analysis['functions'])}\n"
            result += f"- Classes: {len(analysis['classes'])}\n"
            result += f"- Imports: {len(analysis['imports'])}\n"
            result += f"- Complexity score: {analysis['complexity']}\n"

            if analysis['functions']:
                result += "\nFunctions:\n"
                for func in analysis['functions'][:5]:  # Limit to first 5
                    result += f"  - {func['name']}() at line {func['line']}\n"

            if analysis['classes']:
                result += "\nClasses:\n"
                for cls in analysis['classes'][:5]:  # Limit to first 5
                    result += f"  - {cls['name']} at line {cls['line']}\n"

            if analysis['issues']:
                result += "\nIssues:\n"
                for issue in analysis['issues'][:5]:  # Limit to first 5
                    result += f"  - {issue}\n"

            return result

        except SyntaxError as e:
            return f"Syntax error in Python code: {e}"
        except Exception as e:
            logger.error(f"Error analyzing Python code: {e}")
            return f"Error analyzing code: {str(e)}"

    def _extract_functions(self, code: str, language: str = 'python') -> str:
        """Extract function definitions from code."""
        try:
            if language.lower() == 'python':
                return self._extract_python_functions(code)
            else:
                return self._extract_generic_functions(code, language)
        except Exception as e:
            logger.error(f"Error extracting functions: {e}")
            return f"Error extracting functions: {str(e)}"

    def _extract_python_functions(self, code: str) -> str:
        """Extract Python function definitions."""
        try:
            tree = ast.parse(code)
            functions = []

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # Get function signature
                    args = []
                    for arg in node.args.args:
                        args.append(arg.arg)

                    # Get defaults
                    defaults = []
                    if node.args.defaults:
                        defaults = [ast.unparse(default) for default in node.args.defaults]

                    # Combine args with defaults
                    signature_parts = []
                    num_defaults = len(defaults)
                    for i, arg in enumerate(args):
                        if i >= len(args) - num_defaults:
                            default_idx = i - (len(args) - num_defaults)
                            signature_parts.append(f"{arg}={defaults[default_idx]}")
                        else:
                            signature_parts.append(arg)

                    signature = f"def {node.name}({', '.join(signature_parts)}):"
                    docstring = ast.get_docstring(node) or "No docstring"

                    functions.append({
                        'name': node.name,
                        'signature': signature,
                        'docstring': docstring,
                        'line': node.lineno
                    })

            if not functions:
                return "No functions found in the code."

            result = "Extracted Functions:\n\n"
            for i, func in enumerate(functions, 1):
                result += f"{i}. {func['signature']}\n"
                result += f"   Line: {func['line']}\n"
                result += f"   Doc: {func['docstring'][:100]}...\n\n"

            return result

        except Exception as e:
            return f"Error extracting Python functions: {str(e)}"

    def _extract_generic_functions(self, code: str, language: str) -> str:
        """Extract functions from non-Python code using regex."""
        patterns = {
            'javascript': r'function\s+(\w+)\s*\([^)]*\)\s*\{',
            'java': r'(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\([^)]*\)\s*\{',
            'cpp': r'(?:\w+\s+)*(\w+)\s*\([^)]*\)\s*\{',
            'c': r'(?:\w+\s+)*(\w+)\s*\([^)]*\)\s*\{'
        }

        pattern = patterns.get(language.lower())
        if not pattern:
            return f"Function extraction not supported for language: {language}"

        matches = re.finditer(pattern, code, re.MULTILINE)
        functions = []

        for match in matches:
            function_name = match.group(1)
            line_num = code[:match.start()].count('\n') + 1
            functions.append({
                'name': function_name,
                'line': line_num,
                'match': match.group(0)
            })

        if not functions:
            return f"No functions found in {language} code."

        result = f"Extracted {language} Functions:\n\n"
        for i, func in enumerate(functions, 1):
            result += f"{i}. {func['name']}() at line {func['line']}\n"
            result += f"   Pattern: {func['match']}\n\n"

        return result

    def _extract_classes(self, code: str, language: str = 'python') -> str:
        """Extract class definitions from code."""
        try:
            if language.lower() == 'python':
                return self._extract_python_classes(code)
            else:
                return self._extract_generic_classes(code, language)
        except Exception as e:
            logger.error(f"Error extracting classes: {e}")
            return f"Error extracting classes: {str(e)}"

    def _extract_python_classes(self, code: str) -> str:
        """Extract Python class definitions."""
        try:
            tree = ast.parse(code)
            classes = []

            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # Get base classes
                    bases = []
                    for base in node.bases:
                        if hasattr(base, 'id'):
                            bases.append(base.id)
                        else:
                            bases.append(ast.unparse(base))

                    # Get methods
                    methods = []
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            methods.append(item.name)

                    docstring = ast.get_docstring(node) or "No docstring"

                    classes.append({
                        'name': node.name,
                        'bases': bases,
                        'methods': methods,
                        'docstring': docstring,
                        'line': node.lineno
                    })

            if not classes:
                return "No classes found in the code."

            result = "Extracted Classes:\n\n"
            for i, cls in enumerate(classes, 1):
                result += f"{i}. class {cls['name']}"
                if cls['bases']:
                    result += f"({', '.join(cls['bases'])})"
                result += f":\n"
                result += f"   Line: {cls['line']}\n"
                result += f"   Methods: {', '.join(cls['methods'][:5])}\n"
                if len(cls['methods']) > 5:
                    result += f"   ... and {len(cls['methods']) - 5} more\n"
                result += f"   Doc: {cls['docstring'][:100]}...\n\n"

            return result

        except Exception as e:
            return f"Error extracting Python classes: {str(e)}"

    def _extract_generic_classes(self, code: str, language: str) -> str:
        """Extract classes from non-Python code using regex."""
        patterns = {
            'java': r'(?:public|private|protected)?\s*class\s+(\w+)(?:\s+extends\s+\w+)?(?:\s+implements\s+[\w,\s]+)?\s*\{',
            'cpp': r'class\s+(\w+)(?:\s*:\s*(?:public|private|protected)\s+\w+)?\s*\{',
            'c#': r'(?:public|private|protected|internal)?\s*class\s+(\w+)(?:\s*:\s*\w+)?\s*\{'
        }

        pattern = patterns.get(language.lower())
        if not pattern:
            return f"Class extraction not supported for language: {language}"

        matches = re.finditer(pattern, code, re.MULTILINE)
        classes = []

        for match in matches:
            class_name = match.group(1)
            line_num = code[:match.start()].count('\n') + 1
            classes.append({
                'name': class_name,
                'line': line_num,
                'match': match.group(0)
            })

        if not classes:
            return f"No classes found in {language} code."

        result = f"Extracted {language} Classes:\n\n"
        for i, cls in enumerate(classes, 1):
            result += f"{i}. {cls['name']} at line {cls['line']}\n"
            result += f"   Pattern: {cls['match']}\n\n"

        return result

    def _format_python_code(self, code: str, line_length: int = 88) -> str:
        """Format Python code with proper style."""
        try:
            # Basic formatting
            lines = code.split('\n')
            formatted_lines = []
            indent_level = 0

            for line in lines:
                stripped = line.strip()
                if not stripped:
                    formatted_lines.append('')
                    continue

                # Adjust indent level
                if stripped.startswith(('def ', 'class ', 'if ', 'for ', 'while ', 'with ', 'try:')):
                    formatted_lines.append('    ' * indent_level + stripped)
                    indent_level += 1
                elif stripped.startswith(('except', 'elif', 'else:')):
                    formatted_lines.append('    ' * (indent_level - 1) + stripped)
                elif stripped in ('pass', 'break', 'continue', 'return'):
                    formatted_lines.append('    ' * indent_level + stripped)
                    if stripped == 'return' and indent_level > 0:
                        indent_level -= 1
                else:
                    formatted_lines.append('    ' * indent_level + stripped)

                # Decrease indent for closing statements
                if stripped.endswith(':') and not stripped.startswith(('def ', 'class ', 'if ', 'for ', 'while ', 'with ', 'try:', 'except', 'elif', 'else:')):
                    pass  # Keep current level
                elif any(stripped.startswith(keyword) for keyword in ['return', 'break', 'continue', 'pass']):
                    if indent_level > 0:
                        indent_level = max(0, indent_level - 1)

            formatted_code = '\n'.join(formatted_lines)

            return f"Formatted Python Code:\n```python\n{formatted_code}\n```"

        except Exception as e:
            return f"Error formatting Python code: {str(e)}"

    def _add_docstrings(self, code: str, style: str = 'google') -> str:
        """Add docstrings to Python functions and classes."""
        try:
            tree = ast.parse(code)

            # This is a simplified implementation
            # In a real implementation, you would modify the AST and regenerate code

            result = "Functions and classes that need docstrings:\n\n"

            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                    if not ast.get_docstring(node):
                        node_type = "Function" if isinstance(node, ast.FunctionDef) else "Class"
                        result += f"{node_type}: {node.name} at line {node.lineno}\n"

                        if style == 'google':
                            if isinstance(node, ast.FunctionDef):
                                result += f'    """Brief description of {node.name}.\n\n'
                                if node.args.args:
                                    result += "    Args:\n"
                                    for arg in node.args.args:
                                        if arg.arg != 'self':
                                            result += f"        {arg.arg}: Description of {arg.arg}.\n"
                                result += "\n    Returns:\n        Description of return value.\n    \"\"\"\n\n"
                            else:
                                result += f'    """Brief description of {node.name} class.\n\n'
                                result += "    Attributes:\n        Add attributes here.\n    \"\"\"\n\n"

            return result

        except Exception as e:
            return f"Error adding docstrings: {str(e)}"

    def _generate_test_template(self, code: str, test_framework: str = 'unittest') -> str:
        """Generate test template for given code."""
        try:
            tree = ast.parse(code)
            functions = []
            classes = []

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    classes.append(node.name)

            if test_framework == 'unittest':
                template = "import unittest\nfrom your_module import *\n\n"
                template += "class TestYourModule(unittest.TestCase):\n\n"

                for func in functions:
                    template += f"    def test_{func}(self):\n"
                    template += f"        \"\"\"Test {func} function.\"\"\"\n"
                    template += "        # TODO: Implement test\n"
                    template += "        self.fail('Test not implemented')\n\n"

                for cls in classes:
                    template += f"    def test_{cls.lower()}_creation(self):\n"
                    template += f"        \"\"\"Test {cls} class creation.\"\"\"\n"
                    template += "        # TODO: Implement test\n"
                    template += "        self.fail('Test not implemented')\n\n"

                template += "if __name__ == '__main__':\n    unittest.main()\n"

            return f"Generated Test Template:\n```python\n{template}\n```"

        except Exception as e:
            return f"Error generating test template: {str(e)}"

    def _generate_class_template(self, class_name: str, base_class: str = None, methods: List[str] = None) -> str:
        """Generate class template with common methods."""
        try:
            methods = methods or ['__init__', '__str__', '__repr__']

            template = f"class {class_name}"
            if base_class:
                template += f"({base_class})"
            template += ":\n"
            template += f'    """Brief description of {class_name} class."""\n\n'

            for method in methods:
                if method == '__init__':
                    template += "    def __init__(self):\n"
                    template += f'        """Initialize {class_name} instance."""\n'
                    if base_class:
                        template += "        super().__init__()\n"
                    template += "        # TODO: Add initialization code\n"
                    template += "        pass\n\n"
                elif method == '__str__':
                    template += "    def __str__(self):\n"
                    template += f'        """Return string representation of {class_name}."""\n'
                    template += f"        return f'{class_name}()'\n\n"
                elif method == '__repr__':
                    template += "    def __repr__(self):\n"
                    template += f'        """Return detailed string representation of {class_name}."""\n'
                    template += f"        return f'{class_name}()'\n\n"
                else:
                    template += f"    def {method}(self):\n"
                    template += f'        """Description of {method} method."""\n'
                    template += "        # TODO: Implement method\n"
                    template += "        pass\n\n"

            return f"Generated Class Template:\n```python\n{template}\n```"

        except Exception as e:
            return f"Error generating class template: {str(e)}"

    def _find_function_calls(self, code: str, function_name: str) -> str:
        """Find all calls to a specific function in code."""
        try:
            tree = ast.parse(code)
            calls = []

            for node in ast.walk(tree):
                if isinstance(node, ast.Call):
                    if hasattr(node.func, 'id') and node.func.id == function_name:
                        line_num = node.lineno
                        calls.append(line_num)
                    elif hasattr(node.func, 'attr') and node.func.attr == function_name:
                        line_num = node.lineno
                        calls.append(line_num)

            if not calls:
                return f"No calls to function '{function_name}' found."

            result = f"Found {len(calls)} calls to '{function_name}':\n"
            for line_num in calls:
                result += f"  - Line {line_num}\n"

            return result

        except Exception as e:
            return f"Error finding function calls: {str(e)}"

    def _find_imports(self, code: str, import_type: str = 'all') -> str:
        """Find all import statements in code."""
        try:
            tree = ast.parse(code)
            imports = []

            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append({
                            'type': 'import',
                            'module': alias.name,
                            'alias': alias.asname,
                            'line': node.lineno
                        })
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ''
                    for alias in node.names:
                        imports.append({
                            'type': 'from_import',
                            'module': module,
                            'name': alias.name,
                            'alias': alias.asname,
                            'line': node.lineno
                        })

            if not imports:
                return "No import statements found."

            result = f"Found {len(imports)} import statements:\n\n"
            for imp in imports:
                if imp['type'] == 'import':
                    result += f"Line {imp['line']}: import {imp['module']}"
                    if imp['alias']:
                        result += f" as {imp['alias']}"
                    result += "\n"
                else:
                    result += f"Line {imp['line']}: from {imp['module']} import {imp['name']}"
                    if imp['alias']:
                        result += f" as {imp['alias']}"
                    result += "\n"

            return result

        except Exception as e:
            return f"Error finding imports: {str(e)}"
