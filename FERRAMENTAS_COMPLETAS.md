# ENHANCED MILITARY AGENT - DOCUMENTAÇÃO COMPLETA DAS FERRAMENTAS
## Compatibilidade 10/10 com Augment Agent Auto

### **FERRAMENTAS PRINCIPAIS (10 FERRAMENTAS)**

#### **1. write_file - Criação de Arquivos**
**Finalidade:** Criar arquivos em qualquer local do sistema com suporte completo a Unicode
**Sintaxe:** `{tool.write_file(path='C:\\caminho\\arquivo.txt', content='conteúdo', encoding='utf-8')}`
**Parâmetros:**
- `path` (obrigatório): Caminho completo do arquivo
- `content` (obrigatório): Conteúdo do arquivo
- `encoding` (opcional): Codificação (padrão: utf-8)

**Exemplo de Uso:**
```
{tool.write_file(path='C:\\teste.txt', content='Linha 1\nLinha 2')}
```

**Recursos:**
- ✅ Acesso total ao sistema (C:\\ incluído)
- ✅ Criação automática de diretórios
- ✅ Suporte a Unicode completo
- ✅ Validação de integridade
- ✅ Adição automática ao contexto

---

#### **2. read_file - Leitura de Arquivos**
**Finalidade:** Ler arquivos de qualquer local do sistema com detecção automática de codificação
**Sintaxe:** `{tool.read_file(path='C:\\caminho\\arquivo.txt', encoding='utf-8')}`
**Parâmetros:**
- `path` (obrigatório): Caminho completo do arquivo
- `encoding` (opcional): Codificação específica

**Exemplo de Uso:**
```
{tool.read_file(path='C:\\Windows\\System32\\drivers\\etc\\hosts')}
```

**Recursos:**
- ✅ Detecção automática de codificação
- ✅ Suporte a múltiplas codificações
- ✅ Exibição de metadados (tamanho, linhas)
- ✅ Adição automática ao contexto

---

#### **3. analyze_code - Análise Completa de Código**
**Finalidade:** Análise avançada de código com detecção de finalidade, segurança e qualidade
**Sintaxe:** `{tool.analyze_code(file_path='C:\\caminho\\script.py', language='python')}`
**Parâmetros:**
- `file_path` (obrigatório): Caminho do arquivo de código
- `code` (alternativo): Código direto como string
- `language` (opcional): Linguagem específica

**Exemplo de Uso:**
```
{tool.analyze_code(file_path='C:\\ProtecaoWindows\\protecao_windows.py')}
```

**Recursos:**
- ✅ Análise de finalidade e propósito
- ✅ Detecção de problemas de segurança
- ✅ Análise de qualidade de código
- ✅ Extração de funções e classes
- ✅ Análise de dependências
- ✅ Detecção automática de linguagem

---

#### **4. remember - Sistema de Memória**
**Finalidade:** Adicionar informações à memória do agente com categorização
**Sintaxe:** `{tool.remember(memory_text='informação', tags=['tag1', 'tag2'], importance=5)}`
**Parâmetros:**
- `memory_text` (obrigatório): Texto da memória
- `tags` (opcional): Lista de tags
- `importance` (opcional): Importância de 1-5

**Exemplo de Uso:**
```
{tool.remember(memory_text='Usuário prefere arquivos em C:\\', importance=4)}
```

---

#### **5. get_memories - Recuperação de Memórias**
**Finalidade:** Recuperar memórias armazenadas com filtros
**Sintaxe:** `{tool.get_memories(limit=10, min_importance=3)}`
**Parâmetros:**
- `limit` (opcional): Número máximo de memórias
- `min_importance` (opcional): Importância mínima

---

#### **6. search_memories - Busca Semântica de Memórias**
**Finalidade:** Buscar memórias usando similaridade semântica
**Sintaxe:** `{tool.search_memories(query='busca', limit=5)}`
**Parâmetros:**
- `query` (obrigatório): Termo de busca
- `limit` (opcional): Número de resultados

---

#### **7. search_context - Busca no Contexto**
**Finalidade:** Buscar informações no contexto do agente
**Sintaxe:** `{tool.search_context(query='busca', max_results=5)}`
**Parâmetros:**
- `query` (obrigatório): Termo de busca
- `max_results` (opcional): Número máximo de resultados

---

#### **8. add_to_context - Adicionar ao Contexto**
**Finalidade:** Adicionar informações ao contexto do agente
**Sintaxe:** `{tool.add_to_context(content='conteúdo', path='caminho', metadata={})}`
**Parâmetros:**
- `content` (obrigatório): Conteúdo a adicionar
- `path` (obrigatório): Caminho de referência
- `metadata` (opcional): Metadados adicionais

---

#### **9. format_code - Formatação de Código**
**Finalidade:** Formatar código seguindo boas práticas
**Sintaxe:** `{tool.format_code(code='código', language='python')}`
**Parâmetros:**
- `code` (obrigatório): Código a formatar
- `language` (opcional): Linguagem específica

---

#### **10. execute_command - Execução de Comandos**
**Finalidade:** Executar comandos do sistema com validação de segurança
**Sintaxe:** `{tool.execute_command(command='dir C:\\', timeout=30)}`
**Parâmetros:**
- `command` (obrigatório): Comando a executar
- `timeout` (opcional): Timeout em segundos

**Recursos:**
- ✅ Validação de segurança inteligente
- ✅ Execução com timeout
- ✅ Captura de saída e erros
- ✅ Monitoramento de execução

---

### **FERRAMENTAS DE CÓDIGO (9 FERRAMENTAS)**

#### **11. analyze_python_code - Análise Específica Python**
**Finalidade:** Análise detalhada de código Python
**Sintaxe:** `{tool.analyze_python_code(code='código python')}`

#### **12. extract_functions - Extração de Funções**
**Finalidade:** Extrair todas as funções de um código
**Sintaxe:** `{tool.extract_functions(code='código', language='python')}`

#### **13. extract_classes - Extração de Classes**
**Finalidade:** Extrair todas as classes de um código
**Sintaxe:** `{tool.extract_classes(code='código', language='python')}`

#### **14. format_python_code - Formatação Python**
**Finalidade:** Formatação específica para Python
**Sintaxe:** `{tool.format_python_code(code='código python')}`

#### **15. add_docstrings - Adicionar Docstrings**
**Finalidade:** Adicionar docstrings automáticas ao código
**Sintaxe:** `{tool.add_docstrings(code='código', style='google')}`

#### **16. generate_test_template - Template de Testes**
**Finalidade:** Gerar template de testes para código
**Sintaxe:** `{tool.generate_test_template(code='código', framework='pytest')}`

#### **17. generate_class_template - Template de Classes**
**Finalidade:** Gerar template de classes
**Sintaxe:** `{tool.generate_class_template(class_name='MinhaClasse', methods=['metodo1'])}`

#### **18. find_function_calls - Encontrar Chamadas de Função**
**Finalidade:** Encontrar todas as chamadas de função no código
**Sintaxe:** `{tool.find_function_calls(code='código', function_name='nome_funcao')}`

#### **19. find_imports - Encontrar Imports**
**Finalidade:** Encontrar todas as importações no código
**Sintaxe:** `{tool.find_imports(code='código', language='python')}`

---

### **FERRAMENTAS DE PROCESSO (10 FERRAMENTAS)**

#### **20. start_background_process - Iniciar Processo em Background**
**Finalidade:** Iniciar processos em segundo plano
**Sintaxe:** `{tool.start_background_process(command='comando', working_dir='C:\\')}`

#### **21. check_process_status - Verificar Status de Processo**
**Finalidade:** Verificar status de processos em execução
**Sintaxe:** `{tool.check_process_status(process_id=1234)}`

#### **22. kill_process - Terminar Processo**
**Finalidade:** Terminar processos específicos
**Sintaxe:** `{tool.kill_process(process_id=1234, force=True)}`

#### **23. list_directory - Listar Diretório**
**Finalidade:** Listar conteúdo de diretórios
**Sintaxe:** `{tool.list_directory(path='C:\\', recursive=False, show_hidden=True)}`

#### **24. create_directory - Criar Diretório**
**Finalidade:** Criar diretórios com estrutura completa
**Sintaxe:** `{tool.create_directory(path='C:\\novo\\diretorio', recursive=True)}`

#### **25. copy_file - Copiar Arquivo**
**Finalidade:** Copiar arquivos entre locais
**Sintaxe:** `{tool.copy_file(source='C:\\origem.txt', destination='C:\\destino.txt')}`

#### **26. move_file - Mover Arquivo**
**Finalidade:** Mover/renomear arquivos
**Sintaxe:** `{tool.move_file(source='C:\\origem.txt', destination='C:\\novo_nome.txt')}`

#### **27. get_system_info - Informações do Sistema**
**Finalidade:** Obter informações detalhadas do sistema
**Sintaxe:** `{tool.get_system_info(detailed=True)}`

#### **28. get_environment_variables - Variáveis de Ambiente**
**Finalidade:** Obter variáveis de ambiente do sistema
**Sintaxe:** `{tool.get_environment_variables(filter='PATH')}`

---

### **FERRAMENTAS DE BUSCA (6 FERRAMENTAS)**

#### **29. search_in_files - Buscar em Arquivos**
**Finalidade:** Buscar texto dentro de arquivos
**Sintaxe:** `{tool.search_in_files(pattern='texto', directory='C:\\', file_types=['.txt', '.py'])}`

#### **30. find_files - Encontrar Arquivos**
**Finalidade:** Encontrar arquivos por nome ou padrão
**Sintaxe:** `{tool.find_files(pattern='*.py', directory='C:\\', recursive=True)}`

#### **31. search_code_patterns - Buscar Padrões de Código**
**Finalidade:** Buscar padrões específicos em código
**Sintaxe:** `{tool.search_code_patterns(pattern='def.*:', directory='C:\\', language='python')}`

#### **32. analyze_text_content - Analisar Conteúdo de Texto**
**Finalidade:** Analisar conteúdo textual de arquivos
**Sintaxe:** `{tool.analyze_text_content(file_path='C:\\arquivo.txt', analysis_type='sentiment')}`

#### **33. search_directory_structure - Analisar Estrutura de Diretório**
**Finalidade:** Analisar e mapear estrutura de diretórios
**Sintaxe:** `{tool.search_directory_structure(path='C:\\projeto', max_depth=3)}`

#### **34. find_duplicate_files - Encontrar Arquivos Duplicados**
**Finalidade:** Encontrar arquivos duplicados por hash
**Sintaxe:** `{tool.find_duplicate_files(directory='C:\\', check_content=True)}`

---

### **FERRAMENTAS WEB (5 FERRAMENTAS)**

#### **35. web_search - Busca na Web**
**Finalidade:** Realizar buscas na internet
**Sintaxe:** `{tool.web_search(query='termo de busca', num_results=5, search_engine='duckduckgo')}`

#### **36. fetch_webpage - Buscar Página Web**
**Finalidade:** Baixar e extrair conteúdo de páginas web
**Sintaxe:** `{tool.fetch_webpage(url='https://exemplo.com', extract_text=True)}`

#### **37. search_news - Buscar Notícias**
**Finalidade:** Buscar artigos de notícias recentes
**Sintaxe:** `{tool.search_news(query='tecnologia', num_results=5, days_back=7)}`

#### **38. search_academic - Buscar Artigos Acadêmicos**
**Finalidade:** Buscar papers e artigos acadêmicos
**Sintaxe:** `{tool.search_academic(query='machine learning', num_results=5, year_from=2020)}`

#### **39. search_code - Buscar Código Online**
**Finalidade:** Buscar exemplos de código e repositórios
**Sintaxe:** `{tool.search_code(query='python file handling', language='python', num_results=5)}`

---

## **PROCEDIMENTOS DE USO NÍVEL 10/10 (AUGMENT AGENT AUTO)**

### **1. EXECUÇÃO OBRIGATÓRIA DE FERRAMENTAS**
- ✅ SEMPRE execute ferramentas quando solicitado
- ✅ NUNCA apenas descreva o que faria
- ✅ Use sintaxe exata: `{tool.nome_ferramenta(param='valor')}`

### **2. ACESSO TOTAL AO SISTEMA**
- ✅ Acesso completo ao drive C:\\ e todos os diretórios
- ✅ Criação, modificação e leitura de arquivos em qualquer local
- ✅ Execução de comandos do sistema com validação inteligente
- ✅ Operações de diretório sem restrições

### **3. TRATAMENTO DE ERROS AVANÇADO**
- ✅ Tentativas automáticas com diferentes codificações
- ✅ Criação automática de diretórios quando necessário
- ✅ Fallback para métodos alternativos
- ✅ Relatórios detalhados de erros com soluções

### **4. INTEGRAÇÃO COM CONTEXTO**
- ✅ Adição automática de arquivos lidos ao contexto
- ✅ Indexação de código analisado
- ✅ Memória persistente de operações
- ✅ Busca semântica em todo o histórico

### **5. FORMATAÇÃO DE RESPOSTAS**
- ✅ Uso de `<augment_code_snippet>` para código
- ✅ Emojis e formatação visual clara
- ✅ Relatórios estruturados e detalhados
- ✅ Metadados completos (tamanho, linhas, codificação)

### **6. SEGURANÇA INTELIGENTE**
- ✅ Validação apenas para comandos extremamente perigosos
- ✅ Avisos para operações potencialmente arriscadas
- ✅ Permissão total para operações de arquivo
- ✅ Logs detalhados de todas as operações

**TOTAL: 39 FERRAMENTAS REGISTRADAS E FUNCIONAIS**
**COMPATIBILIDADE: 10/10 COM AUGMENT AGENT AUTO**
