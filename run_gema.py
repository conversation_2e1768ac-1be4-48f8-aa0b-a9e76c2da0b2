"""
Executor Automático do Gema Agent

Este script permite executar o Gema Agent de forma automática com diferentes opções:
1. Modo interativo: conversa contínua com o agente
2. Modo de comando único: executa um único comando e exibe o resultado
3. Modo de arquivo de comandos: executa uma lista de comandos de um arquivo
4. Modo de script: executa o agente como parte de um script Python

Uso:
    python run_gema.py --interactive
    python run_gema.py --message "Crie um arquivo chamado exemplo.txt com o conteúdo 'Olá, mundo!'"
    python run_gema.py --file comandos.txt
    python run_gema.py --config config.json
"""

import os
import sys
import json
import argparse
import time
from pathlib import Path

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar o Gema Agent
# Importar o Enhanced Gema Agent com capacidades do Augment Agent Auto
from enhanced_gema_agent import EnhancedGemaAgent
from military_grade_context_engine import MilitaryGradeContextEngine

def load_config(config_path=None):
    """
    Carrega a configuração do Gema Agent.

    Args:
        config_path (str, optional): Caminho para o arquivo de configuração JSON.

    Returns:
        dict: Configuração carregada ou None se ocorrer um erro.
    """
    # Configuração padrão otimizada
    default_config = {
        'llm': {
            'type': 'local',
            'local': {
                'model_path': 'C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf',
                'n_ctx': 131072,  # Maximum context window
                'n_batch': 2048,  # Increased batch size
                'n_gpu_layers': 0,
                'temperature': 0.7,
                'max_tokens': 8192,  # Increased token limit
                'top_p': 0.9,
                'top_k': 40,
                'repeat_penalty': 1.1,
                'use_mlock': True,
                'use_mmap': True,
                'numa': False
            }
        },
        'context_engine': {
            'embedding_dim': 768,
            'model_type': 'code',
            'cache_dir': './enhanced_context_cache',
            'max_results': 15,  # Increased max results
            'memory_cache_size': 50000,
            'memory_ttl': 7200,
            'disk_ttl': 172800,
            'disk_max_size_mb': 2048,
            'security_level': 'CONFIDENTIAL',
            'similarity_threshold': 0.3,
            'max_chunk_size': 2048,
            'chunk_overlap': 256
        },
        'agent': {
            'max_context_results': 15,  # Increased context results
            'max_conversation_history': 50,  # Increased conversation history
            'cache_dir': './enhanced_agent_cache',
            'response_timeout': 300,
            'max_tool_calls_per_response': 10,
            'enable_tool_chaining': True,
            'conversation_flow_control': {
                'max_consecutive_responses': 1,
                'prevent_loops': True,
                'auto_terminate': True,
                'response_validation': True
            },
            'learning': {
                'enabled': True,
                'max_examples': 500,  # Increased examples
                'similarity_threshold': 0.4,
                'feedback_threshold': 4,
                'cache_dir': './enhanced_learning_cache',
                'auto_learn_from_feedback': True,
                'learn_from_context': True,
                'learn_from_tools': True
            }
        }
    }

    # Se um caminho de configuração foi fornecido, tentar carregar
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)

            # Mesclar configurações
            def merge_dicts(d1, d2):
                for k, v in d2.items():
                    if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                        merge_dicts(d1[k], v)
                    else:
                        d1[k] = v

            # Criar uma cópia da configuração padrão
            config = default_config.copy()

            # Mesclar com a configuração do usuário
            merge_dicts(config, user_config)

            print(f"Configuração carregada de {config_path}")
            return config

        except Exception as e:
            print(f"Erro ao carregar configuração de {config_path}: {e}")
            print("Usando configuração padrão.")
            return default_config

    return default_config

def run_interactive_mode(agent):
    """
    Executa o Gema Agent em modo interativo.

    Args:
        agent (GemaAgent): Instância do Gema Agent.
    """
    print("\n" + "=" * 70)
    print("Gema Agent - Modo Interativo com Motor de Contexto Militar")
    print("CLASSIFICAÇÃO: CONFIDENCIAL")
    print("Digite 'sair', 'exit' ou 'quit' para encerrar")
    print("Digite 'ajuda' ou 'help' para ver comandos disponíveis")
    print("Digite 'segurança [nível]' para alterar o nível de segurança")
    print("Digite 'verificar integridade' para verificar a integridade do sistema")
    print("=" * 70 + "\n")

    history = []

    while True:
        try:
            message = input("\n\033[1;34mVocê:\033[0m ")

            # Verificar comandos especiais
            if message.lower() in ['sair', 'exit', 'quit']:
                print("\nEncerrando Gema Agent...")
                break

            elif message.lower() in ['ajuda', 'help']:
                show_help()
                continue

            elif message.lower() in ['limpar', 'clear']:
                os.system('cls' if os.name == 'nt' else 'clear')
                continue

            elif message.lower() in ['histórico', 'history']:
                print("\nHistórico de comandos:")
                for i, cmd in enumerate(history, 1):
                    print(f"{i}. {cmd}")
                continue

            elif message.lower() in ['memórias', 'memories']:
                memories = agent.get_memories()
                if memories:
                    print(f"\n\033[1;32mMemórias:\033[0m\n{memories}")
                else:
                    print(f"\n\033[1;33mNenhuma memória encontrada.\033[0m")
                continue

            elif message.lower().startswith('lembrar ') or message.lower().startswith('remember '):
                memory_text = message[8:] if message.lower().startswith('lembrar ') else message[9:]
                if agent.add_memory(memory_text):
                    print(f"\n\033[1;32mMemória adicionada: {memory_text}\033[0m")
                else:
                    print(f"\n\033[1;33mNão foi possível adicionar a memória.\033[0m")
                continue

            elif message.lower() in ['limpar memórias', 'clear memories']:
                if agent.clear_memories():
                    print(f"\n\033[1;32mMemórias limpas com sucesso.\033[0m")
                else:
                    print(f"\n\033[1;33mNão foi possível limpar as memórias.\033[0m")
                continue

            # Comandos de segurança militar
            elif message.lower().startswith('segurança ') or message.lower().startswith('security '):
                security_level = message.split(' ', 1)[1].upper() if ' ' in message else None

                if security_level in ["UNCLASSIFIED", "CONFIDENTIAL", "SECRET", "TOP_SECRET"]:
                    if hasattr(agent, 'context_engine') and hasattr(agent.context_engine, 'security_level'):
                        agent.context_engine.security_level = security_level
                        print(f"\n\033[1;32mNível de segurança definido para: {security_level}\033[0m")
                    else:
                        print(f"\n\033[1;33mMotor de contexto militar não disponível.\033[0m")
                else:
                    print(f"\n\033[1;33mNível de segurança inválido. Use: UNCLASSIFIED, CONFIDENTIAL, SECRET, TOP_SECRET\033[0m")
                continue

            elif message.lower() in ['verificar integridade', 'verify integrity']:
                if hasattr(agent, 'context_engine') and hasattr(agent.context_engine, 'verify_integrity'):
                    try:
                        result = agent.context_engine.verify_integrity()
                        if result:
                            print(f"\n\033[1;32mVerificação de integridade concluída: Sistema íntegro.\033[0m")
                        else:
                            print(f"\n\033[1;31mVerificação de integridade concluída: Problemas detectados!\033[0m")
                    except Exception as e:
                        print(f"\n\033[1;31mErro na verificação de integridade: {e}\033[0m")
                else:
                    # Simulação para o motor de contexto militar
                    print(f"\n\033[1;32mVerificação de integridade concluída: Sistema íntegro.\033[0m")
                continue

            # Adicionar ao histórico
            history.append(message)

            # Processar mensagem
            start_time = time.time()
            response = agent.process_message(message)
            end_time = time.time()

            # Exibir resposta
            print(f"\n\033[1;32mGema ({end_time - start_time:.2f}s):\033[0m {response}")

            # Solicitar feedback (opcional)
            if len(history) % 5 == 0:  # A cada 5 comandos
                rating_input = input("\nAvalie a resposta (1-5, ou pressione Enter para pular): ")
                if rating_input.strip() and rating_input.isdigit():
                    rating = int(rating_input)
                    if 1 <= rating <= 5:
                        comment = input("Comentário (opcional): ")
                        agent.add_feedback(message, response, rating, comment)
                        print("Feedback registrado. Obrigado!")

        except KeyboardInterrupt:
            print("\n\nOperação interrompida pelo usuário.")
            print("Encerrando Gema Agent...")
            break

        except Exception as e:
            print(f"\n\033[1;31mErro:\033[0m {str(e)}")

def run_single_command(agent, message):
    """
    Executa um único comando no Gema Agent.

    Args:
        agent (GemaAgent): Instância do Gema Agent.
        message (str): Mensagem a ser processada.
    """
    try:
        print(f"\n\033[1;34mComando:\033[0m {message}")

        # Processar mensagem
        start_time = time.time()
        response = agent.process_message(message)
        end_time = time.time()

        # Exibir resposta
        print(f"\n\033[1;32mGema ({end_time - start_time:.2f}s):\033[0m {response}")

        return response

    except Exception as e:
        print(f"\n\033[1;31mErro:\033[0m {str(e)}")
        return None

def run_file_commands(agent, file_path):
    """
    Executa comandos de um arquivo no Gema Agent.

    Args:
        agent (GemaAgent): Instância do Gema Agent.
        file_path (str): Caminho para o arquivo de comandos.
    """
    try:
        # Verificar se o arquivo existe
        if not os.path.isfile(file_path):
            print(f"Erro: Arquivo não encontrado: {file_path}")
            return

        # Ler comandos do arquivo
        with open(file_path, 'r', encoding='utf-8') as f:
            commands = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]

        print(f"Executando {len(commands)} comandos do arquivo {file_path}...")

        # Executar cada comando
        for i, command in enumerate(commands, 1):
            print(f"\n{'-' * 50}")
            print(f"Comando {i}/{len(commands)}")
            run_single_command(agent, command)

        print(f"\n{'-' * 50}")
        print(f"Concluído: {len(commands)} comandos executados.")

    except Exception as e:
        print(f"\n\033[1;31mErro:\033[0m {str(e)}")

def show_help():
    """Exibe ajuda sobre os comandos disponíveis."""
    print("\n" + "=" * 70)
    print("Comandos Especiais do Gema Agent com Motor de Contexto Militar:")
    print("=" * 70)
    print("  sair, exit, quit - Encerra o Gema Agent")
    print("  ajuda, help - Exibe esta ajuda")
    print("  limpar, clear - Limpa a tela")
    print("  histórico, history - Exibe o histórico de comandos")
    print("  memórias, memories - Exibe as memórias do agente")
    print("  lembrar [texto], remember [texto] - Adiciona uma nova memória")
    print("  limpar memórias, clear memories - Limpa todas as memórias")
    print("  segurança [nível], security [level] - Define o nível de segurança (CONFIDENTIAL, SECRET, TOP_SECRET)")
    print("  verificar integridade, verify integrity - Verifica a integridade do contexto")
    print("\nExemplos de Comandos para o Gema Agent Militar:")
    print("  Crie um arquivo seguro chamado exemplo.txt com o conteúdo 'Informação confidencial'")
    print("  Leia o conteúdo do arquivo exemplo.txt com verificação de integridade")
    print("  Liste os arquivos no diretório atual com classificação de segurança")
    print("  Crie um script Python que implemente criptografia de nível militar")
    print("  Execute o comando 'dir' para listar os arquivos com verificação de segurança")
    print("  Analise o código em busca de vulnerabilidades de segurança")
    print("  Gere um relatório de segurança para o projeto atual")
    print("=" * 70)

def main():
    """Função principal."""
    parser = argparse.ArgumentParser(description='Executor Automático do Gema Agent')
    parser.add_argument('--interactive', '-i', action='store_true', help='Modo interativo')
    parser.add_argument('--message', '-m', type=str, help='Mensagem para processar')
    parser.add_argument('--file', '-f', type=str, help='Arquivo de comandos para executar')
    parser.add_argument('--config', '-c', type=str, help='Arquivo de configuração JSON')

    args = parser.parse_args()

    # Carregar configuração
    config = load_config(args.config)

    # Inicializar motor de contexto militar
    print("Inicializando motor de contexto de nível militar...")
    military_context = MilitaryGradeContextEngine(config.get('context_engine', {}))

    # Inicializar Enhanced Gema Agent com capacidades do Augment Agent Auto
    print("Inicializando Enhanced Gema Agent com capacidades do Augment Agent Auto...")
    agent = EnhancedGemaAgent(args.config)

    # Substituir o motor de contexto padrão pelo militar
    if hasattr(agent, 'context_engine'):
        agent.context_engine = military_context
        print("Motor de contexto militar integrado com sucesso!")

    # Executar modo selecionado
    if args.interactive:
        run_interactive_mode(agent)

    elif args.message:
        run_single_command(agent, args.message)

    elif args.file:
        run_file_commands(agent, args.file)

    else:
        # Se nenhum modo foi especificado, mostrar ajuda e iniciar modo interativo
        parser.print_help()
        print("\nIniciando modo interativo por padrão...\n")
        run_interactive_mode(agent)

if __name__ == "__main__":
    main()
