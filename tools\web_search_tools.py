"""
Enhanced Web Search Tools for Military Agent - Matching Augment Agent Auto

This module provides web search capabilities that match
Augment Agent Auto's web search functionality.
"""

import requests
import json
import logging
import time
from typing import Dict, List, Any, Optional
from urllib.parse import quote_plus, urljoin
import re

from .enhanced_tool_interface import BaseTool<PERSON>rovider, ToolRegistry

logger = logging.getLogger("WEB_SEARCH_TOOLS")


class WebSearchTools(BaseToolProvider):
    """
    Enhanced web search tools matching Augment Agent Auto's web capabilities.
    """

    def __init__(self, registry: ToolRegistry):
        """Initialize web search tools."""
        super().__init__(registry, "web_search")
        
        # Configure search settings
        self.search_engines = {
            'duckduckgo': 'https://api.duckduckgo.com/',
            'searx': 'https://searx.org/search',
            'google_custom': None  # Requires API key
        }
        
        self.user_agent = "Enhanced Military Agent/1.0 (Web Search)"
        self.timeout = 30
        self.max_results = 10

    def setup_tools(self) -> None:
        """Setup enhanced web search tools."""

        # Web search tools
        self.register_tool(
            "web_search",
            "Search the web for information using multiple search engines",
            self._web_search,
            parameters={
                'query': {'type': str, 'required': True},
                'num_results': {'type': int, 'default': 5},
                'search_engine': {'type': str, 'default': 'duckduckgo'},
                'safe_search': {'type': bool, 'default': True}
            }
        )

        self.register_tool(
            "fetch_webpage",
            "Fetch and extract content from a webpage",
            self._fetch_webpage,
            parameters={
                'url': {'type': str, 'required': True},
                'extract_text': {'type': bool, 'default': True},
                'follow_redirects': {'type': bool, 'default': True}
            }
        )

        self.register_tool(
            "search_news",
            "Search for recent news articles",
            self._search_news,
            parameters={
                'query': {'type': str, 'required': True},
                'num_results': {'type': int, 'default': 5},
                'days_back': {'type': int, 'default': 7}
            }
        )

        self.register_tool(
            "search_academic",
            "Search for academic papers and research",
            self._search_academic,
            parameters={
                'query': {'type': str, 'required': True},
                'num_results': {'type': int, 'default': 5},
                'year_from': {'type': int, 'default': None}
            }
        )

        self.register_tool(
            "search_code",
            "Search for code examples and repositories",
            self._search_code,
            parameters={
                'query': {'type': str, 'required': True},
                'language': {'type': str, 'default': None},
                'num_results': {'type': int, 'default': 5}
            }
        )

    def _web_search(self, query: str, num_results: int = 5, 
                   search_engine: str = 'duckduckgo', safe_search: bool = True) -> str:
        """Search the web for information."""
        try:
            logger.info(f"Web search: {query} (engine: {search_engine})")
            
            if search_engine == 'duckduckgo':
                return self._search_duckduckgo(query, num_results, safe_search)
            elif search_engine == 'searx':
                return self._search_searx(query, num_results, safe_search)
            else:
                return f"Search engine '{search_engine}' not supported. Available: duckduckgo, searx"
                
        except Exception as e:
            logger.error(f"Web search error: {e}")
            return f"Error performing web search: {str(e)}"

    def _search_duckduckgo(self, query: str, num_results: int, safe_search: bool) -> str:
        """Search using DuckDuckGo."""
        try:
            # DuckDuckGo Instant Answer API
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }
            
            if safe_search:
                params['safe_search'] = 'strict'
            
            headers = {'User-Agent': self.user_agent}
            
            response = requests.get(
                'https://api.duckduckgo.com/',
                params=params,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                
                results = []
                
                # Add instant answer if available
                if data.get('Abstract'):
                    results.append({
                        'title': data.get('Heading', 'Instant Answer'),
                        'snippet': data.get('Abstract'),
                        'url': data.get('AbstractURL', ''),
                        'source': data.get('AbstractSource', 'DuckDuckGo')
                    })
                
                # Add related topics
                for topic in data.get('RelatedTopics', [])[:num_results-1]:
                    if isinstance(topic, dict) and 'Text' in topic:
                        results.append({
                            'title': topic.get('Text', '').split(' - ')[0],
                            'snippet': topic.get('Text', ''),
                            'url': topic.get('FirstURL', ''),
                            'source': 'DuckDuckGo'
                        })
                
                if not results:
                    return f"No results found for: {query}"
                
                # Format results
                formatted_results = f"Web Search Results for: {query}\n\n"
                for i, result in enumerate(results[:num_results], 1):
                    formatted_results += f"{i}. {result['title']}\n"
                    formatted_results += f"   {result['snippet'][:200]}...\n"
                    formatted_results += f"   URL: {result['url']}\n"
                    formatted_results += f"   Source: {result['source']}\n\n"
                
                return formatted_results
            else:
                return f"Search request failed with status: {response.status_code}"
                
        except Exception as e:
            return f"DuckDuckGo search error: {str(e)}"

    def _search_searx(self, query: str, num_results: int, safe_search: bool) -> str:
        """Search using SearX (privacy-focused search)."""
        try:
            # Use public SearX instance
            searx_url = "https://searx.be/search"
            
            params = {
                'q': query,
                'format': 'json',
                'categories': 'general'
            }
            
            if safe_search:
                params['safesearch'] = '2'
            
            headers = {'User-Agent': self.user_agent}
            
            response = requests.get(
                searx_url,
                params=params,
                headers=headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                if not results:
                    return f"No results found for: {query}"
                
                # Format results
                formatted_results = f"Web Search Results for: {query}\n\n"
                for i, result in enumerate(results[:num_results], 1):
                    formatted_results += f"{i}. {result.get('title', 'No title')}\n"
                    formatted_results += f"   {result.get('content', 'No description')[:200]}...\n"
                    formatted_results += f"   URL: {result.get('url', '')}\n\n"
                
                return formatted_results
            else:
                return f"SearX search failed with status: {response.status_code}"
                
        except Exception as e:
            return f"SearX search error: {str(e)}"

    def _fetch_webpage(self, url: str, extract_text: bool = True, 
                      follow_redirects: bool = True) -> str:
        """Fetch and extract content from a webpage."""
        try:
            logger.info(f"Fetching webpage: {url}")
            
            headers = {
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }
            
            response = requests.get(
                url,
                headers=headers,
                timeout=self.timeout,
                allow_redirects=follow_redirects
            )
            
            if response.status_code == 200:
                content = response.text
                
                if extract_text:
                    # Simple text extraction (remove HTML tags)
                    text_content = re.sub(r'<[^>]+>', '', content)
                    text_content = re.sub(r'\s+', ' ', text_content).strip()
                    
                    # Limit content length
                    if len(text_content) > 2000:
                        text_content = text_content[:2000] + "..."
                    
                    return f"Webpage Content from {url}:\n\n{text_content}"
                else:
                    return f"Raw HTML from {url}:\n\n{content[:2000]}..."
            else:
                return f"Failed to fetch webpage. Status: {response.status_code}"
                
        except Exception as e:
            logger.error(f"Webpage fetch error: {e}")
            return f"Error fetching webpage: {str(e)}"

    def _search_news(self, query: str, num_results: int = 5, days_back: int = 7) -> str:
        """Search for recent news articles."""
        try:
            # Use DuckDuckGo with news-specific query
            news_query = f"{query} news recent"
            return self._search_duckduckgo(news_query, num_results, True)
            
        except Exception as e:
            return f"News search error: {str(e)}"

    def _search_academic(self, query: str, num_results: int = 5, year_from: int = None) -> str:
        """Search for academic papers and research."""
        try:
            # Use DuckDuckGo with academic-specific query
            academic_query = f"{query} research paper academic"
            if year_from:
                academic_query += f" after:{year_from}"
            
            return self._search_duckduckgo(academic_query, num_results, True)
            
        except Exception as e:
            return f"Academic search error: {str(e)}"

    def _search_code(self, query: str, language: str = None, num_results: int = 5) -> str:
        """Search for code examples and repositories."""
        try:
            # Use DuckDuckGo with code-specific query
            code_query = f"{query} code example"
            if language:
                code_query += f" {language}"
            code_query += " site:github.com OR site:stackoverflow.com"
            
            return self._search_duckduckgo(code_query, num_results, True)
            
        except Exception as e:
            return f"Code search error: {str(e)}"
