"""
Teste rápido das correções implementadas
Executa o comando exato que estava falhando
"""

import os
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Teste rápido do comando que estava falhando."""
    
    # Criar arquivo de teste simples
    test_code = '''import os
import sys

def protecao_sistema():
    """Sistema de proteção para Windows."""
    print("Proteção ativa")
    return True

if __name__ == "__main__":
    protecao_sistema()
'''
    
    # Criar diretório e arquivo
    os.makedirs("C:\\ProtecaoWindows", exist_ok=True)
    with open("C:\\ProtecaoWindows\\protecao_windows.py", 'w') as f:
        f.write(test_code)
    
    print("🧪 TESTE RÁPIDO - COMANDO QUE ESTAVA FALHANDO")
    print("="*60)
    
    try:
        from enhanced_gema_agent import EnhancedGemaAgent
        
        agent = EnhancedGemaAgent()
        print(f"✅ Agente carregado com {len(agent.tool_registry.tools)} ferramentas")
        
        # Comando exato que estava falhando
        command = "analise o arquivo C:\\ProtecaoWindows\\protecao_windows.py com a ferramenta analyze_code e me diga para que serve o arquivo , respota em 50 tokens"
        
        print(f"\\n📝 Executando: {command}")
        print("\\n🤖 Resposta:")
        print("-"*40)
        
        response = agent.process_message(command)
        print(response)
        
        print("-"*40)
        
        # Verificar se funcionou
        if "bad escape" in response:
            print("❌ FALHA: Erro de escape ainda presente")
        elif "FERRAMENTA EXECUTADA" in response or "análise" in response.lower():
            print("✅ SUCESSO: Comando funcionou!")
        else:
            print("⚠️ PARCIAL: Resposta gerada mas pode não estar completa")
            
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Limpeza
        try:
            os.remove("C:\\ProtecaoWindows\\protecao_windows.py")
            os.rmdir("C:\\ProtecaoWindows")
        except:
            pass

if __name__ == "__main__":
    main()
