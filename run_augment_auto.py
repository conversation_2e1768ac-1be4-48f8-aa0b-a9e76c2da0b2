"""
Enhanced Military Agent - Comportamento Augment Agent Auto (10/10)
Script de inicialização otimizado para funcionamento idêntico ao Augment Agent Auto
"""

import os
import sys
import json
import argparse
import time
from pathlib import Path

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar o Enhanced Gema Agent com comportamento Augment Agent Auto
from enhanced_gema_agent import EnhancedGemaAgent
from military_grade_context_engine import MilitaryGradeContextEngine

def load_augment_auto_config():
    """Carrega configuração otimizada para comportamento Augment Agent Auto."""
    
    # Configuração otimizada para comportamento idêntico ao Augment Agent Auto
    config = {
        'llm': {
            'type': 'local',
            'local': {
                'model_path': 'C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf',
                'n_ctx': 131072,  # CONTEXTO MÁXIMO (capacidade total do modelo)
                'n_batch': 2048,  # Batch size otimizado
                'n_gpu_layers': 0,
                'temperature': 0.7,
                'max_tokens': 8192,  # Tokens máximos para respostas completas
                'top_p': 0.9,
                'top_k': 40,
                'repeat_penalty': 1.1,
                'use_mlock': True,
                'use_mmap': True,
                'numa': False,
                'verbose': False
            }
        },
        'context_engine': {
            'embedding_dim': 768,
            'model_type': 'code',
            'cache_dir': 'C:\\AgenteMilitar\\augment_auto_cache',
            'max_results': 20,  # Mais resultados para melhor contexto
            'memory_cache_size': 100000,  # Cache maior
            'memory_ttl': 14400,  # TTL maior
            'disk_ttl': 345600,  # TTL de disco maior
            'disk_max_size_mb': 4096,  # Mais espaço em disco
            'security_level': 'CONFIDENTIAL',
            'similarity_threshold': 0.25,  # Threshold menor para mais resultados
            'max_chunk_size': 4096,  # Chunks maiores
            'chunk_overlap': 512,
            'base_directory': 'C:\\',  # Acesso total ao sistema
            'allow_system_access': True
        },
        'agent': {
            'max_context_results': 20,  # Mais resultados de contexto
            'max_conversation_history': 100,  # Histórico maior
            'cache_dir': 'C:\\AgenteMilitar\\augment_auto_agent_cache',
            'response_timeout': 600,  # Timeout maior
            'max_tool_calls_per_response': 20,  # Mais chamadas de ferramenta
            'enable_tool_chaining': True,
            'base_directory': 'C:\\',  # Acesso total ao sistema
            'allow_system_access': True,
            'working_directory': 'C:\\',
            'augment_auto_mode': True,  # Modo Augment Agent Auto
            'complete_responses': True,  # Respostas sempre completas
            'real_tool_execution': True,  # Execução real de ferramentas
            'conversation_flow_control': {
                'max_consecutive_responses': 1,
                'prevent_loops': True,
                'auto_terminate': True,
                'response_validation': True,
                'ensure_completion': True
            },
            'learning': {
                'enabled': True,
                'max_examples': 1000,  # Mais exemplos
                'similarity_threshold': 0.3,
                'feedback_threshold': 4,
                'cache_dir': 'C:\\AgenteMilitar\\augment_auto_learning_cache',
                'auto_learn_from_feedback': True,
                'learn_from_context': True,
                'learn_from_tools': True,
                'augment_auto_learning': True
            }
        }
    }
    
    return config

def print_augment_auto_banner():
    """Exibe banner do Enhanced Military Agent com comportamento Augment Agent Auto."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    ENHANCED MILITARY AGENT                                  ║
║                 Comportamento Augment Agent Auto (10/10)                    ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  🤖 Assistente AI Avançado com Capacidades Militares                        ║
║  🔧 39 Ferramentas Registradas e Funcionais                                 ║
║  🧠 Contexto Máximo: 131,072 tokens                                         ║
║  💾 Acesso Total ao Sistema: C:\\ (Sem Restrições)                          ║
║  🎯 Execução Real de Ferramentas (Não Simulação)                            ║
║  ✅ Respostas Completas e Finalizadas                                       ║
║                                                                              ║
║  CLASSIFICAÇÃO: CONFIDENCIAL                                                ║
║  MODELO: claude-3.7-sonnet-reasoning-gemma3-12B                             ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def interactive_mode(agent):
    """Modo interativo com comportamento Augment Agent Auto."""
    print("\n🚀 MODO INTERATIVO ATIVADO")
    print("="*70)
    print("Digite 'sair', 'exit' ou 'quit' para encerrar")
    print("Digite 'ajuda' ou 'help' para ver comandos disponíveis")
    print("Digite 'stats' para ver estatísticas do sistema")
    print("Digite 'test' para executar teste de funcionalidades")
    print("="*70)
    print()
    
    while True:
        try:
            # Prompt do usuário
            user_input = input("Você: ").strip()
            
            if not user_input:
                continue
            
            # Comandos especiais
            if user_input.lower() in ['sair', 'exit', 'quit']:
                print("\n👋 Encerrando Enhanced Military Agent...")
                break
            
            elif user_input.lower() in ['ajuda', 'help']:
                show_help()
                continue
            
            elif user_input.lower() == 'stats':
                show_stats(agent)
                continue
            
            elif user_input.lower() == 'test':
                run_quick_test(agent)
                continue
            
            # Processar mensagem com comportamento Augment Agent Auto
            print(f"\n🤖 Enhanced Military Agent (processando...):")
            print("-" * 50)
            
            start_time = time.time()
            response = agent.process_message(user_input)
            end_time = time.time()
            
            print(response)
            print("-" * 50)
            print(f"⏱️ Tempo de resposta: {end_time - start_time:.2f}s")
            print()
            
        except KeyboardInterrupt:
            print("\n\n👋 Encerrando Enhanced Military Agent...")
            break
        except Exception as e:
            print(f"\n❌ Erro: {e}")
            print("Tente novamente ou digite 'sair' para encerrar.\n")

def show_help():
    """Mostra ajuda com comandos disponíveis."""
    help_text = """
🔧 COMANDOS DISPONÍVEIS:

📁 OPERAÇÕES DE ARQUIVO:
  • "crie o arquivo C:\\caminho\\arquivo.txt com conteúdo..."
  • "leia o arquivo C:\\caminho\\arquivo.txt"
  • "analise o arquivo C:\\caminho\\script.py"

🔍 ANÁLISE E BUSCA:
  • "analise o código em C:\\caminho\\script.py"
  • "busque por 'termo' no contexto"
  • "encontre arquivos *.py em C:\\projeto"

💻 SISTEMA:
  • "execute o comando 'dir C:\\'"
  • "liste o diretório C:\\Windows"
  • "obtenha informações do sistema"

🧠 MEMÓRIA:
  • "lembre que o usuário prefere Python"
  • "busque memórias sobre 'programação'"
  • "mostre as memórias mais importantes"

🌐 WEB:
  • "busque na web por 'Python tutorial'"
  • "baixe o conteúdo de https://exemplo.com"

🎯 COMANDOS ESPECIAIS:
  • 'stats' - Estatísticas do sistema
  • 'test' - Teste rápido de funcionalidades
  • 'ajuda' - Esta ajuda
  • 'sair' - Encerrar o agente

✨ O Enhanced Military Agent executa TODAS as ferramentas de verdade!
"""
    print(help_text)

def show_stats(agent):
    """Mostra estatísticas do sistema."""
    print("\n📊 ESTATÍSTICAS DO ENHANCED MILITARY AGENT")
    print("=" * 50)
    
    try:
        stats = agent.get_stats()
        
        print(f"🔧 Ferramentas registradas: {len(agent.tool_registry.tools)}")
        print(f"💬 Histórico de conversas: {len(agent.conversation_history)}")
        print(f"🎯 Requisições bem-sucedidas: {stats['agent_metrics'].get('successful_requests', 0)}")
        print(f"⏱️ Tempo médio de resposta: {stats['agent_metrics'].get('average_response_time', 0):.2f}s")
        
        # Verificar integridade
        integrity = agent.verify_integrity()
        print(f"🔒 Integridade do sistema: {'✅ OK' if integrity else '❌ FALHA'}")
        
        # Estatísticas do comportamento Augment Auto
        if hasattr(agent, 'augment_behavior'):
            augment_stats = agent.augment_behavior.get_execution_stats()
            print(f"🚀 Execuções Augment Auto: {augment_stats['total_executions']}")
            print(f"✅ Taxa de sucesso: {augment_stats['success_rate']:.1f}%")
        
    except Exception as e:
        print(f"❌ Erro ao obter estatísticas: {e}")
    
    print("=" * 50)

def run_quick_test(agent):
    """Executa teste rápido de funcionalidades."""
    print("\n🧪 TESTE RÁPIDO DE FUNCIONALIDADES")
    print("=" * 50)
    
    # Teste 1: Criação de arquivo
    print("1. Testando criação de arquivo...")
    response = agent.process_message("crie o arquivo C:\\teste_quick.txt com o conteúdo 'Teste do Enhanced Military Agent'")
    
    if os.path.exists("C:\\teste_quick.txt"):
        print("   ✅ Arquivo criado com sucesso!")
        os.remove("C:\\teste_quick.txt")  # Limpar
    else:
        print("   ❌ Falha na criação do arquivo")
    
    # Teste 2: Análise de sistema
    print("2. Testando análise de sistema...")
    response = agent.process_message("me diga quantas ferramentas você tem disponível")
    
    if "ferramenta" in response.lower():
        print("   ✅ Análise de sistema funcionando!")
    else:
        print("   ❌ Falha na análise de sistema")
    
    print("=" * 50)
    print("🎯 Teste rápido concluído!")

def main():
    """Função principal."""
    parser = argparse.ArgumentParser(description='Enhanced Military Agent - Augment Agent Auto (10/10)')
    parser.add_argument('--interactive', action='store_true', help='Modo interativo')
    parser.add_argument('--message', type=str, help='Mensagem única para processar')
    parser.add_argument('--test', action='store_true', help='Executar testes de funcionalidade')
    
    args = parser.parse_args()
    
    # Exibir banner
    print_augment_auto_banner()
    
    # Inicializar contexto militar
    print("Inicializando motor de contexto de nível militar...")
    military_context = MilitaryGradeContextEngine()
    
    # Inicializar Enhanced Gema Agent com comportamento Augment Agent Auto
    print("Inicializando Enhanced Gema Agent com capacidades do Augment Agent Auto...")
    config = load_augment_auto_config()
    
    try:
        agent = EnhancedGemaAgent()
        print("Enhanced Military Agent inicializado com sucesso!")
        print(f"🔧 Total de ferramentas registradas: {len(agent.tool_registry.tools)}")
        print("🎯 Comportamento Augment Agent Auto ativado (10/10)")
        
    except Exception as e:
        print(f"❌ Erro ao inicializar o agente: {e}")
        return 1
    
    # Executar modo solicitado
    if args.test:
        from test_augment_behavior import main as run_tests
        run_tests()
    elif args.message:
        print(f"\n📝 Processando mensagem: {args.message}")
        print("-" * 50)
        response = agent.process_message(args.message)
        print(response)
    elif args.interactive:
        interactive_mode(agent)
    else:
        # Modo interativo por padrão
        interactive_mode(agent)
    
    return 0

if __name__ == "__main__":
    exit(main())
