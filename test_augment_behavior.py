"""
Teste do comportamento Augment Agent Auto
Verifica se o Enhanced Military Agent está funcionando exatamente como Augment Agent Auto
"""

import os
import sys
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_gema_agent import EnhancedGemaAgent

def test_file_creation():
    """Testa criação de arquivo como Augment Agent Auto."""
    print("🧪 TESTE 1: Criação de arquivo")
    print("="*50)
    
    agent = EnhancedGemaAgent()
    
    # Teste de criação de arquivo
    message = "crie o arquivo c:\\teste_augment.txt com as linhas 1.Primeira linha e linha 2.Segunda linha"
    
    print(f"📝 Comando: {message}")
    print("\n🤖 Resposta do agente:")
    print("-"*30)
    
    start_time = time.time()
    response = agent.process_message(message)
    end_time = time.time()
    
    print(response)
    print("-"*30)
    print(f"⏱️ Tempo de resposta: {end_time - start_time:.2f}s")
    
    # Verificar se arquivo foi criado
    if os.path.exists("c:\\teste_augment.txt"):
        print("✅ SUCESSO: Arquivo foi criado!")
        with open("c:\\teste_augment.txt", 'r') as f:
            content = f.read()
        print(f"📄 Conteúdo: {repr(content)}")
    else:
        print("❌ FALHA: Arquivo não foi criado!")
    
    print("\n" + "="*50 + "\n")

def test_file_analysis():
    """Testa análise de arquivo como Augment Agent Auto."""
    print("🧪 TESTE 2: Análise de arquivo")
    print("="*50)
    
    agent = EnhancedGemaAgent()
    
    # Criar arquivo de teste primeiro
    test_code = '''
import os
import sys

def protecao_sistema():
    """Função de proteção do sistema Windows."""
    if os.name == 'nt':
        print("Sistema Windows detectado")
        return True
    return False

class ProtecaoWindows:
    def __init__(self):
        self.ativo = True
    
    def verificar_integridade(self):
        return self.ativo

if __name__ == "__main__":
    protecao = ProtecaoWindows()
    if protecao.verificar_integridade():
        print("Sistema protegido")
'''
    
    # Criar arquivo de teste
    with open("c:\\teste_protecao.py", 'w') as f:
        f.write(test_code)
    
    # Teste de análise
    message = "analise o arquivo c:\\teste_protecao.py com a ferramenta analyze_code e me diga para que serve"
    
    print(f"📝 Comando: {message}")
    print("\n🤖 Resposta do agente:")
    print("-"*30)
    
    start_time = time.time()
    response = agent.process_message(message)
    end_time = time.time()
    
    print(response)
    print("-"*30)
    print(f"⏱️ Tempo de resposta: {end_time - start_time:.2f}s")
    
    # Verificar se análise foi completa
    if "ANÁLISE COMPLETA" in response and "FINALIDADE" in response:
        print("✅ SUCESSO: Análise completa realizada!")
    else:
        print("❌ FALHA: Análise incompleta!")
    
    print("\n" + "="*50 + "\n")

def test_tool_execution():
    """Testa execução real de ferramentas."""
    print("🧪 TESTE 3: Execução de ferramentas")
    print("="*50)
    
    agent = EnhancedGemaAgent()
    
    # Teste de múltiplas ferramentas
    message = "liste o diretório c:\\ e me mostre os primeiros 5 itens"
    
    print(f"📝 Comando: {message}")
    print("\n🤖 Resposta do agente:")
    print("-"*30)
    
    start_time = time.time()
    response = agent.process_message(message)
    end_time = time.time()
    
    print(response)
    print("-"*30)
    print(f"⏱️ Tempo de resposta: {end_time - start_time:.2f}s")
    
    # Verificar se ferramenta foi executada
    if "FERRAMENTA EXECUTADA" in response or "RESULTADO" in response:
        print("✅ SUCESSO: Ferramenta executada!")
    else:
        print("❌ FALHA: Ferramenta não executada!")
    
    print("\n" + "="*50 + "\n")

def test_complete_response():
    """Testa se respostas são completas como Augment Agent Auto."""
    print("🧪 TESTE 4: Completude de resposta")
    print("="*50)
    
    agent = EnhancedGemaAgent()
    
    # Teste de resposta completa
    message = "me ajude a entender como funciona o sistema de arquivos do Windows"
    
    print(f"📝 Comando: {message}")
    print("\n🤖 Resposta do agente:")
    print("-"*30)
    
    start_time = time.time()
    response = agent.process_message(message)
    end_time = time.time()
    
    print(response)
    print("-"*30)
    print(f"⏱️ Tempo de resposta: {end_time - start_time:.2f}s")
    
    # Verificar completude
    completeness_indicators = [
        "Windows",
        "sistema de arquivos",
        "concluído" in response.lower() or "completo" in response.lower(),
        len(response) > 200,
        "mais alguma" in response.lower() or "posso ajudar" in response.lower()
    ]
    
    complete_count = sum(1 for indicator in completeness_indicators if indicator)
    
    if complete_count >= 3:
        print("✅ SUCESSO: Resposta completa!")
    else:
        print("❌ FALHA: Resposta incompleta!")
    
    print(f"📊 Indicadores de completude: {complete_count}/5")
    print("\n" + "="*50 + "\n")

def test_augment_stats():
    """Testa estatísticas do sistema."""
    print("🧪 TESTE 5: Estatísticas do sistema")
    print("="*50)
    
    agent = EnhancedGemaAgent()
    
    # Obter estatísticas
    stats = agent.get_stats()
    
    print("📊 Estatísticas do Enhanced Military Agent:")
    print(f"- Total de ferramentas: {len(agent.tool_registry.tools)}")
    print(f"- Histórico de conversas: {len(agent.conversation_history)}")
    print(f"- Métricas do agente: {stats['agent_metrics']}")
    
    # Verificar integridade
    integrity = agent.verify_integrity()
    print(f"- Integridade do sistema: {'✅ OK' if integrity else '❌ FALHA'}")
    
    # Estatísticas do comportamento Augment Auto
    if hasattr(agent, 'augment_behavior'):
        augment_stats = agent.augment_behavior.get_execution_stats()
        print(f"- Execuções Augment Auto: {augment_stats}")
    
    print("\n" + "="*50 + "\n")

def main():
    """Executa todos os testes."""
    print("🚀 TESTE COMPLETO DO ENHANCED MILITARY AGENT")
    print("Verificando comportamento idêntico ao Augment Agent Auto (10/10)")
    print("="*70)
    print()
    
    try:
        # Executar todos os testes
        test_file_creation()
        test_file_analysis()
        test_tool_execution()
        test_complete_response()
        test_augment_stats()
        
        print("🎯 RESUMO DOS TESTES")
        print("="*50)
        print("✅ Todos os testes foram executados")
        print("📋 Verifique os resultados acima para confirmar funcionamento")
        print("🤖 Enhanced Military Agent deve estar funcionando como Augment Agent Auto")
        
    except Exception as e:
        print(f"❌ ERRO durante os testes: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Limpeza
        try:
            if os.path.exists("c:\\teste_augment.txt"):
                os.remove("c:\\teste_augment.txt")
            if os.path.exists("c:\\teste_protecao.py"):
                os.remove("c:\\teste_protecao.py")
        except:
            pass

if __name__ == "__main__":
    main()
