{"llm": {"type": "local", "local": {"model_path": "C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf", "n_ctx": 131072, "n_batch": 2048, "n_gpu_layers": 0, "temperature": 0.7, "max_tokens": 8192, "top_p": 0.9, "top_k": 40, "repeat_penalty": 1.1, "mirostat": 2, "mirostat_tau": 5.0, "mirostat_eta": 0.1, "seed": -1, "threads": -1, "use_mlock": true, "use_mmap": true, "numa": false, "verbose": false}}, "context_engine": {"embedding_dim": 768, "model_type": "code", "cache_dir": "./enhanced_context_cache", "max_results": 15, "memory_cache_size": 50000, "memory_ttl": 7200, "disk_ttl": 172800, "disk_max_size_mb": 2048, "security_level": "CONFIDENTIAL", "similarity_threshold": 0.3, "max_chunk_size": 2048, "chunk_overlap": 256, "enable_semantic_search": true, "enable_fuzzy_search": true, "enable_regex_search": true}, "agent": {"max_context_results": 15, "max_conversation_history": 50, "cache_dir": "./enhanced_agent_cache", "response_timeout": 300, "max_tool_calls_per_response": 10, "enable_tool_chaining": true, "enable_parallel_tools": false, "conversation_flow_control": {"max_consecutive_responses": 1, "prevent_loops": true, "auto_terminate": true, "response_validation": true}, "learning": {"enabled": true, "max_examples": 500, "similarity_threshold": 0.4, "feedback_threshold": 4, "cache_dir": "./enhanced_learning_cache", "auto_learn_from_feedback": true, "learn_from_context": true, "learn_from_tools": true}, "memory": {"max_memories": 10000, "auto_cleanup": true, "cleanup_threshold": 0.3, "importance_decay": 0.95, "enable_clustering": true, "enable_summarization": true}, "security": {"validate_file_paths": true, "validate_commands": true, "sandbox_mode": false, "allowed_extensions": [".py", ".txt", ".md", ".json", ".yaml", ".yml", ".js", ".html", ".css", ".sql"], "blocked_commands": ["rm -rf", "del /f", "format", "fdisk", "shutdown", "reboot"], "max_file_size_mb": 100}}, "tools": {"web_search": {"enabled": true, "max_results": 10, "timeout": 30, "user_agent": "Enhanced Military Agent/1.0"}, "code_analysis": {"enabled": true, "max_file_size_mb": 50, "supported_languages": ["python", "javascript", "java", "cpp", "c", "go", "rust", "typescript"], "enable_security_scan": true, "enable_quality_scan": true, "enable_complexity_analysis": true}, "file_operations": {"enabled": true, "max_file_size_mb": 100, "backup_on_overwrite": true, "verify_encoding": true, "auto_detect_encoding": true}, "process_management": {"enabled": true, "max_processes": 5, "default_timeout": 60, "enable_monitoring": true, "auto_cleanup": true}}, "logging": {"level": "INFO", "file": "enhanced_gema_agent.log", "max_size_mb": 100, "backup_count": 5, "format": "%(asctime)s - [%(levelname)s] - %(name)s - %(message)s"}, "performance": {"enable_caching": true, "cache_ttl": 3600, "max_cache_size_mb": 512, "enable_compression": true, "enable_parallel_processing": false, "max_workers": 4}}