"""
Teste específico para correção do problema de análise de arquivo
Verifica se o Enhanced Military Agent consegue analisar C:\ProtecaoWindows\protecao_windows.py
"""

import os
import sys
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_file():
    """Cria arquivo de teste para análise."""
    test_code = '''"""
Sistema de Proteção Windows
Módulo responsável pela proteção e monitoramento do sistema Windows
"""

import os
import sys
import subprocess
import winreg
from datetime import datetime

class ProtecaoWindows:
    """
    Classe principal para proteção do sistema Windows.
    Implementa funcionalidades de monitoramento e segurança.
    """
    
    def __init__(self):
        self.ativo = True
        self.nivel_protecao = "ALTO"
        self.log_eventos = []
        
    def verificar_integridade_sistema(self):
        """Verifica a integridade dos arquivos do sistema."""
        try:
            # Verificar arquivos críticos do sistema
            arquivos_criticos = [
                "C:\\Windows\\System32\\kernel32.dll",
                "C:\\Windows\\System32\\ntdll.dll",
                "C:\\Windows\\System32\\user32.dll"
            ]
            
            for arquivo in arquivos_criticos:
                if not os.path.exists(arquivo):
                    self.log_evento(f"ALERTA: Arquivo crítico não encontrado: {arquivo}")
                    return False
            
            return True
            
        except Exception as e:
            self.log_evento(f"ERRO na verificação de integridade: {e}")
            return False
    
    def monitorar_processos_suspeitos(self):
        """Monitora processos suspeitos no sistema."""
        try:
            # Lista de processos suspeitos conhecidos
            processos_suspeitos = [
                "malware.exe",
                "virus.exe",
                "trojan.exe",
                "keylogger.exe"
            ]
            
            # Obter lista de processos ativos
            resultado = subprocess.run(
                ["tasklist", "/fo", "csv"], 
                capture_output=True, 
                text=True
            )
            
            if resultado.returncode == 0:
                processos_ativos = resultado.stdout.lower()
                
                for processo_suspeito in processos_suspeitos:
                    if processo_suspeito in processos_ativos:
                        self.log_evento(f"ALERTA: Processo suspeito detectado: {processo_suspeito}")
                        self.bloquear_processo(processo_suspeito)
            
        except Exception as e:
            self.log_evento(f"ERRO no monitoramento de processos: {e}")
    
    def verificar_registro_windows(self):
        """Verifica chaves críticas do registro do Windows."""
        try:
            # Chaves críticas para verificar
            chaves_criticas = [
                (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"),
                (winreg.HKEY_CURRENT_USER, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run")
            ]
            
            for hive, caminho in chaves_criticas:
                try:
                    chave = winreg.OpenKey(hive, caminho)
                    
                    # Enumerar valores na chave
                    i = 0
                    while True:
                        try:
                            nome, valor, tipo = winreg.EnumValue(chave, i)
                            self.verificar_entrada_startup(nome, valor)
                            i += 1
                        except WindowsError:
                            break
                    
                    winreg.CloseKey(chave)
                    
                except WindowsError as e:
                    self.log_evento(f"Erro ao acessar registro: {e}")
            
        except Exception as e:
            self.log_evento(f"ERRO na verificação do registro: {e}")
    
    def verificar_entrada_startup(self, nome, valor):
        """Verifica se uma entrada de startup é suspeita."""
        entradas_suspeitas = [
            "malware",
            "virus",
            "trojan",
            "backdoor",
            "keylogger"
        ]
        
        for suspeita in entradas_suspeitas:
            if suspeita in nome.lower() or suspeita in valor.lower():
                self.log_evento(f"ALERTA: Entrada suspeita no startup: {nome} = {valor}")
                return True
        
        return False
    
    def bloquear_processo(self, nome_processo):
        """Bloqueia um processo suspeito."""
        try:
            subprocess.run(["taskkill", "/f", "/im", nome_processo], check=True)
            self.log_evento(f"Processo bloqueado: {nome_processo}")
        except subprocess.CalledProcessError:
            self.log_evento(f"Falha ao bloquear processo: {nome_processo}")
    
    def log_evento(self, mensagem):
        """Registra um evento no log."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        evento = f"[{timestamp}] {mensagem}"
        self.log_eventos.append(evento)
        print(evento)
    
    def executar_protecao_completa(self):
        """Executa proteção completa do sistema."""
        self.log_evento("Iniciando proteção completa do sistema Windows")
        
        # Verificar integridade
        if self.verificar_integridade_sistema():
            self.log_evento("Integridade do sistema: OK")
        else:
            self.log_evento("ALERTA: Problemas de integridade detectados")
        
        # Monitorar processos
        self.monitorar_processos_suspeitos()
        
        # Verificar registro
        self.verificar_registro_windows()
        
        self.log_evento("Proteção completa finalizada")
    
    def obter_relatorio(self):
        """Obtém relatório completo da proteção."""
        relatorio = {
            "status": "ATIVO" if self.ativo else "INATIVO",
            "nivel_protecao": self.nivel_protecao,
            "total_eventos": len(self.log_eventos),
            "eventos_recentes": self.log_eventos[-10:] if self.log_eventos else []
        }
        return relatorio

def main():
    """Função principal do sistema de proteção."""
    print("Sistema de Proteção Windows - Iniciando...")
    
    # Criar instância da proteção
    protecao = ProtecaoWindows()
    
    # Executar proteção completa
    protecao.executar_protecao_completa()
    
    # Obter e exibir relatório
    relatorio = protecao.obter_relatorio()
    print(f"\\nRelatório Final:")
    print(f"Status: {relatorio['status']}")
    print(f"Nível de Proteção: {relatorio['nivel_protecao']}")
    print(f"Total de Eventos: {relatorio['total_eventos']}")

if __name__ == "__main__":
    main()
'''
    
    # Criar diretório se não existir
    os.makedirs("C:\\ProtecaoWindows", exist_ok=True)
    
    # Criar arquivo
    with open("C:\\ProtecaoWindows\\protecao_windows.py", 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print("✅ Arquivo de teste criado: C:\\ProtecaoWindows\\protecao_windows.py")

def test_file_analysis():
    """Testa análise do arquivo específico."""
    print("🧪 TESTE DE ANÁLISE DE ARQUIVO")
    print("="*50)
    
    # Criar arquivo de teste
    create_test_file()
    
    try:
        from enhanced_gema_agent import EnhancedGemaAgent
        
        # Inicializar agente
        print("Inicializando Enhanced Military Agent...")
        agent = EnhancedGemaAgent()
        print(f"✅ Agente inicializado com {len(agent.tool_registry.tools)} ferramentas")
        
        # Teste da mensagem exata que estava falhando
        message = "analise o arquivo C:\\ProtecaoWindows\\protecao_windows.py com a ferramenta analyze_code e me diga para que serve o arquivo , respota em 50 tokens"
        
        print(f"\\n📝 Comando: {message}")
        print("\\n🤖 Resposta do agente:")
        print("-"*50)
        
        start_time = time.time()
        response = agent.process_message(message)
        end_time = time.time()
        
        print(response)
        print("-"*50)
        print(f"⏱️ Tempo de resposta: {end_time - start_time:.2f}s")
        
        # Verificar se a análise foi bem-sucedida
        success_indicators = [
            "ANÁLISE COMPLETA" in response or "análise" in response.lower(),
            "protecao" in response.lower() or "proteção" in response.lower(),
            "windows" in response.lower(),
            "FERRAMENTA EXECUTADA" in response,
            len(response) > 100
        ]
        
        success_count = sum(success_indicators)
        
        print(f"\\n📊 RESULTADO DO TESTE:")
        print(f"Indicadores de sucesso: {success_count}/5")
        
        if success_count >= 3:
            print("✅ SUCESSO: Análise funcionando corretamente!")
        else:
            print("❌ FALHA: Análise não funcionou como esperado")
            
        # Verificar se não houve erro de escape
        if "bad escape" not in response:
            print("✅ SUCESSO: Erro de escape corrigido!")
        else:
            print("❌ FALHA: Erro de escape ainda presente")
            
        return success_count >= 3
        
    except Exception as e:
        print(f"❌ ERRO durante o teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_extraction_function():
    """Testa função de extração de caminho isoladamente."""
    print("\\n🧪 TESTE DE EXTRAÇÃO DE CAMINHO")
    print("="*50)
    
    try:
        from augment_auto_behavior import AugmentAutoBehavior
        
        # Criar instância para testar
        behavior = AugmentAutoBehavior(None, None, None)
        
        # Testar mensagens problemáticas
        test_messages = [
            "analise o arquivo C:\\ProtecaoWindows\\protecao_windows.py com a ferramenta analyze_code",
            "analise o arquivo C:\\ProtecaoWindows\\protecao_windows.py e me diga para que serve",
            "use analyze_code em C:\\ProtecaoWindows\\protecao_windows.py",
            "C:\\ProtecaoWindows\\protecao_windows.py deve ser analisado"
        ]
        
        print("Testando extração de caminhos:")
        for i, message in enumerate(test_messages, 1):
            extracted = behavior._extract_file_path(message)
            print(f"{i}. '{message[:50]}...'")
            print(f"   Extraído: {extracted}")
            
            if extracted == "C:\\ProtecaoWindows\\protecao_windows.py":
                print("   ✅ CORRETO")
            else:
                print("   ❌ INCORRETO")
            print()
        
    except Exception as e:
        print(f"❌ ERRO no teste de extração: {e}")

def main():
    """Executa todos os testes."""
    print("🚀 TESTE DE CORREÇÃO - ANÁLISE DE ARQUIVO")
    print("Verificando se o problema de escape foi corrigido")
    print("="*70)
    
    try:
        # Teste 1: Extração de caminho
        test_extraction_function()
        
        # Teste 2: Análise completa
        success = test_file_analysis()
        
        print("\\n🎯 RESUMO FINAL")
        print("="*50)
        if success:
            print("✅ CORREÇÃO BEM-SUCEDIDA!")
            print("O Enhanced Military Agent agora funciona corretamente")
            print("Problema de escape de caracteres resolvido")
        else:
            print("❌ CORREÇÃO INCOMPLETA")
            print("Ainda há problemas a serem resolvidos")
        
    except Exception as e:
        print(f"❌ ERRO geral: {e}")
    
    finally:
        # Limpeza
        try:
            if os.path.exists("C:\\ProtecaoWindows\\protecao_windows.py"):
                os.remove("C:\\ProtecaoWindows\\protecao_windows.py")
            if os.path.exists("C:\\ProtecaoWindows"):
                os.rmdir("C:\\ProtecaoWindows")
        except:
            pass

if __name__ == "__main__":
    main()
